{"name": "galaxy-vue3-demi", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@unhead/vue": "^2.0.11", "axios": "^1.10.0", "core-js": "^3.43.0", "element-plus": "^2.10.2", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "svg-sprite-loader": "^6.0.11", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/mockjs": "^1.0.10", "@types/node": "^24.0.7", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.4.3", "husky": "^9.1.7", "jsdom": "^25.0.1", "mockjs": "^1.1.0", "sass": "^1.89.2", "svgo": "^4.0.0", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-plugin-mock": "^3.0.2", "vitest": "^2.1.8", "vue-tsc": "^2.1.10"}}